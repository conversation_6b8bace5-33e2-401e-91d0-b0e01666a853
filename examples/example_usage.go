package main

import (
	"context"
	"fmt"
	"log"

	"github.com/google/uuid"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	gr "github.com/ikateclab/gorm-repository"
)

// User represents a user entity
type User struct {
	ID     uuid.UUID `gorm:"type:text;primary_key"`
	Name   string
	Email  string
	Age    int
	Active bool
}

// Implement Diffable interface for smart updates
func (u User) Clone() User {
	return User{
		ID:     u.ID,
		Name:   u.Name,
		Email:  u.Email,
		Age:    u.Age,
		Active: u.Active,
	}
}

func (u User) Diff(other User) map[string]interface{} {
	diff := make(map[string]interface{})
	
	if u.Name != other.Name {
		diff["name"] = u.Name
	}
	if u.Email != other.Email {
		diff["email"] = u.Email
	}
	if u.Age != other.Age {
		diff["age"] = u.Age
	}
	if u.Active != other.Active {
		diff["active"] = u.Active
	}
	
	return diff
}

func main() {
	// Setup database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Auto-migrate
	err = db.AutoMigrate(&User{})
	if err != nil {
		log.Fatal("Failed to migrate:", err)
	}

	// Create repository - note we use User (value type), not *User
	userRepo := gr.NewGormRepository[User](db)
	ctx := context.Background()

	// Example 1: Create a user
	user := User{
		ID:     uuid.New(),
		Name:   "John Doe",
		Email:  "<EMAIL>",
		Age:    30,
		Active: true,
	}

	// Create expects *User, so we pass &user
	err = userRepo.Create(ctx, &user)
	if err != nil {
		log.Fatal("Failed to create user:", err)
	}
	fmt.Printf("Created user: %+v\n", user)

	// Example 2: Find by ID - returns *User
	foundUser, err := userRepo.FindById(ctx, user.ID)
	if err != nil {
		log.Fatal("Failed to find user:", err)
	}
	fmt.Printf("Found user: %+v\n", *foundUser)

	// Example 3: FindOne - returns *User
	oneUser, err := userRepo.FindOne(ctx, gr.WithQuery(func(db *gorm.DB) *gorm.DB {
		return db.Where("email = ?", user.Email)
	}))
	if err != nil {
		log.Fatal("Failed to find one user:", err)
	}
	fmt.Printf("Found one user: %+v\n", *oneUser)

	// Example 4: FindMany - returns []*User
	users, err := userRepo.FindMany(ctx, gr.WithQuery(func(db *gorm.DB) *gorm.DB {
		return db.Where("active = ?", true)
	}))
	if err != nil {
		log.Fatal("Failed to find users:", err)
	}
	fmt.Printf("Found %d users\n", len(users))
	for i, u := range users {
		fmt.Printf("User %d: %+v\n", i+1, *u)
	}

	// Example 5: Update using Save - expects *User
	foundUser.Age = 31
	foundUser.Name = "John Updated"
	err = userRepo.Save(ctx, foundUser)
	if err != nil {
		log.Fatal("Failed to save user:", err)
	}
	fmt.Printf("Updated user: %+v\n", *foundUser)

	// Example 6: Update using UpdateByIdWithMap - returns *User
	updatedUser, err := userRepo.UpdateByIdWithMap(ctx, user.ID, map[string]interface{}{
		"age": 32,
		"name": "John Map Updated",
	})
	if err != nil {
		log.Fatal("Failed to update user with map:", err)
	}
	fmt.Printf("Map updated user: %+v\n", *updatedUser)

	// Example 7: Pagination - returns *PaginationResult[*User]
	result, err := userRepo.FindPaginated(ctx, 1, 10)
	if err != nil {
		log.Fatal("Failed to paginate users:", err)
	}
	fmt.Printf("Pagination result: Total=%d, CurrentPage=%d, Data count=%d\n", 
		result.Total, result.CurrentPage, len(result.Data))
	for i, u := range result.Data {
		fmt.Printf("Paginated user %d: %+v\n", i+1, *u)
	}

	fmt.Println("\n✅ All examples completed successfully!")
	fmt.Println("\n📝 Key points about the new interface:")
	fmt.Println("- Repository is defined with value type: NewGormRepository[User](db)")
	fmt.Println("- All methods that accept entities expect *T (pointers)")
	fmt.Println("- All methods that return entities return *T (pointers)")
	fmt.Println("- FindMany returns []*T (slice of pointers)")
	fmt.Println("- FindPaginated returns *PaginationResult[*T]")
	fmt.Println("- This provides consistent pointer semantics throughout the API")
}
